<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room">
            </div>
            <button class="sort-btn">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs" [formGroup]="FilterForm">
            <div class="facility-dropdown">
                <select id="ddlFacility" class="form-control" formControlName="ddlFacility" (change)="FilterPatientDetails()"
                 [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <option value="">---Select Facility---</option>
                    <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        <span>{{s.facilityName}}</span>
                    </option>
                </select>
            </div>
            <div class="tabs">
                <button class="tab-btn" [class.active]="activeTab === 'myPatients'"
                    (click)="setActiveTab('myPatients')">
                    My Patients
                </button>
                <button class="tab-btn" [class.active]="activeTab === 'hidden'" (click)="setActiveTab('hidden')">
                    Hidden
                </button>
            </div>
        </div>
    </div>

    <!-- Patient Cards -->
    <div class="patient-cards">
        <div class="patient-card p-3 flex flex-col">
            <div class="flex flex-col p-6">
                <div class="flex">
                    <span class="text-lg font-medium">Shark Watermelon</span>
                    <div class="mx-2 h-6 border-l-2"></div>
                    <span class="font-normal">02/02/79 (46 F)</span>
                    <div class="mx-2 h-6 border-l-2"></div>
                    <span class="font-normal">TRMC</span>
                </div>
                <div class="my-2 h-1 w-12 border-t-2"></div>
                <div class="grid w-full grid-cols-2 gap-x-4">
                    <div>V00000000000123</div>
                    <div>022OT-B</div>
                    <div>Inpatient</div>
                    <div class="flex items-center">
                        <span>LOS:2</span>
                        <div class="mx-2 h-4 border-l-2"></div>
                        <span>GLOS:4</span>
                    </div>
                </div>
                <div class="mt-2 flex items-center">
                    <div>
                        <img src="assets/icons/icon-price-transparency.svg" class="img-icon">
                    </div>
                    <div class="ml-2">
                        Humana Medicare
                    </div>
                </div>
                <div class="mt-2 flex items-center">
                    <div>
                        <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                    </div>
                    <div class="ml-2">
                        Zebra Cantaloupe, Tiger Cherry
                    </div>
                </div>
            </div>
            <div class="action-buttons">
                <button class="action-btn delete-btn">
                    <img src="assets/icons/icon-trash-red.svg" class="img-icon px-6">
                </button>
                <button class="action-btn lightning-btn">
                    <img src="assets/icons/icon-hide.svg" class="img-icon px-6">
                </button>
                <button class="action-btn refresh-btn" [matMenuTriggerFor]="summaryMenu">
                    <img src="assets/icons/icon-change.svg" class="img-icon px-6">
                </button>
                <button class="action-btn play-btn" (click)="openDetails()">
                    <img src="assets/icons/icon-start.svg" class="img-icon px-6">
                </button>
                <button class="action-btn menu-btn" (click)="openActionsPopup('Shark Watermelon')">
                    <img src="assets/icons/icon-more.svg" class="img-icon">
                </button>
                
            </div>
        </div>
        <div class="patient-card p-3 flex flex-col">
            <div class="flex flex-col p-6">
                <div class="flex">
                    <span class="text-lg font-medium">Shark Watermelon</span>
                    <div class="mx-2 h-6 border-l-2"></div>
                    <span class="font-normal">02/02/79 (46 F)</span>
                    <div class="mx-2 h-6 border-l-2"></div>
                    <span class="font-normal">TRMC</span>
                </div>
                <div class="my-2 h-1 w-12 border-t-2"></div>
                <div class="grid w-full grid-cols-2 gap-x-4">
                    <div>V00000000000123</div>
                    <div>022OT-B</div>
                    <div>Inpatient</div>
                    <div class="flex items-center">
                        <span>LOS:2</span>
                        <div class="mx-2 h-4 border-l-2"></div>
                        <span>GLOS:4</span>
                    </div>
                </div>
                <div class="mt-2 flex items-center">
                    <div>
                        <img src="assets/icons/icon-price-transparency.svg" class="img-icon">
                    </div>
                    <div class="ml-2">
                        Humana Medicare
                    </div>
                </div>
                <div class="mt-2 flex items-center">
                    <div>
                        <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                    </div>
                    <div class="ml-2">
                        Zebra Cantaloupe, Tiger Cherry
                    </div>
                </div>
            </div>
            <div class="action-buttons">
                <button class="action-btn delete-btn">
                    <img src="assets/icons/icon-trash-red.svg" class="img-icon px-6">
                </button>
                <!-- <button class="action-btn lightning-btn">
                    <img src="assets/icons/icon-hide.svg" class="img-icon px-6">
                </button>
                <button class="action-btn refresh-btn">
                    <img src="assets/icons/icon-change.svg" class="img-icon px-6">
                </button>
                <button class="action-btn play-btn" (click)="openDetails()">
                    <img src="assets/icons/icon-start.svg" class="img-icon px-6">
                </button>
                <button class="action-btn menu-btn">
                    <img src="assets/icons/icon-more.svg" class="img-icon">
                </button> -->
            </div>
        </div>
    </div>

</div>

